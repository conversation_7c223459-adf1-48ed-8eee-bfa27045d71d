# 学术研究项目 .gitignore

# 操作系统生成的文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Windows
Desktop.ini
$RECYCLE.BIN/

# 临时文件
*.tmp
*.temp
*~
*.swp
*.swo

# 编辑器和IDE
.vscode/
.idea/
*.sublime-project
*.sublime-workspace
.atom/

# 文档编辑临时文件
~$*.docx
~$*.pptx
~$*.xlsx
*.tmp

# LaTeX 相关文件
*.aux
*.bbl
*.blg
*.fdb_latexmk
*.fls
*.log
*.out
*.synctex.gz
*.toc
*.nav
*.snm
*.vrb

# 数据文件 (敏感数据不应提交)
*.csv
*.xlsx
*.xls
*.dta
*.sav
*.rds
*.RData
data/
raw_data/
processed_data/
confidential/

# 分析结果和输出
results/
output/
figures/
plots/
*.png
*.jpg
*.jpeg
*.pdf
*.eps

# R 相关
.Rproj.user/
.Rhistory
.RData
.Ruserdata
*.Rproj

# Python 相关
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
.venv/
pip-log.txt
pip-delete-this-directory.txt

# Jupyter Notebook
.ipynb_checkpoints/
*.ipynb

# Stata 相关
*.dta.backup
*.smcl

# SPSS 相关
*.sav.backup

# 备份文件
*.bak
*.backup
*_backup.*
backup_*

# 压缩文件
*.zip
*.rar
*.7z
*.tar.gz

# 个人笔记和草稿
personal_notes/
drafts/
scratch/
todo.txt
notes.txt

# 会议和演示文件的备份
presentations_backup/
slides_backup/

# 参考文献管理
*.bib.backup
*.ris.backup

# 其他
.env
.secret
config.ini
